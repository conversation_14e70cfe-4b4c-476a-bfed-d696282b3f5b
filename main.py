import pygame
from settings import WIDTH, HEIGHT, FPS
from map import GameMap
from player import Player
from ui import UI
from enemies import *
from boss import Boss, Boss_defeated, Boss_trophy

pygame.init()
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("PROJEKT - ISAAC")
clock = pygame.time.Clock()

pocisk_img = pygame.image.load("assets/bullet.png").convert_alpha()
pocisk_img = pygame.transform.scale(pocisk_img, (100, 100)) #poczatkowa wielkosc pocisku
enemy_img = pygame.image.load("assets/enemy_stand_shoot.png").convert_alpha()
enemy2_img = pygame.image.load("assets/spider.png").convert_alpha()
enemy_bullet_img = pygame.image.load("assets/enemy_bullet.png").convert_alpha()
bullet_group = pygame.sprite.Group()
game_map = GameMap()
current_coords = (1, 1)
current_room = game_map.rooms[current_coords]
enemy_group = pygame.sprite.Group()
enemy_bullets = pygame.sprite.Group()
boss_death_group = pygame.sprite.Group()
trophy_group = pygame.sprite.Group()
boss_defeated_animation_done = False

# Функція для створення ворогів у кімнаті
def spawn_enemies_for_room(room_coords):
    global boss, boss_defeated_animation_done
    enemy_group.empty()
    if room_coords == (1, 1):
        # 1-ша кімната: пуста
        boss = None
    elif room_coords == (2, 1):
        # 2-га кімната: 2x EnemyStandShoot
        enemy1 = EnemyStandShoot(300, 250, enemy_img)
        enemy2 = EnemyStandShoot(500, 350, enemy_img)
        enemy_group.add(enemy1, enemy2)
        boss = None
    elif room_coords == (2, 2):
        # 3-тя кімната: 1x EnemyStandShoot + 1x EnemyCharge
        enemy1 = EnemyStandShoot(400, 250, enemy_img)
        enemy2 = EnemyCharge(300, 350, enemy2_img)
        enemy_group.add(enemy1, enemy2)
        boss = None
    elif room_coords == (3, 2):
        # 4-та кімната: 2x EnemyStandShoot + 1x EnemyCharge
        enemy1 = EnemyStandShoot(300, 250, enemy_img)
        enemy2 = EnemyStandShoot(500, 250, enemy_img)
        enemy3 = EnemyCharge(400, 400, enemy2_img)
        enemy_group.add(enemy1, enemy2, enemy3)
        boss = None
    elif room_coords == (4, 2):
        # 5-та кімната: Boss
        if boss is None:
            boss = Boss()
            boss_defeated_animation_done = False

# Створюємо ворогів для початкової кімнати
spawn_enemies_for_room(current_coords)

player = Player()
ui = UI(player)
boss = None  # Босс буде створений тільки в останній кімнаті
boss_death_pos = None  # Позиція смерті босса для трофею
death_couldown = 120
invicible=60
pain = 0
clash = Clash(sila_odepchniecia=140)
transition_cooldown = 0
running = True

while running:
    global boss_death_pos
    clock.tick(FPS)
    keys = pygame.key.get_pressed()

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
    
    if keys[pygame.K_UP]:
        player.shoot(bullet_group,pocisk_img,(0, -1)) 
    if keys[pygame.K_DOWN]:
        player.shoot(bullet_group,pocisk_img,(0, 1))
    if keys[pygame.K_LEFT]: 
        player.shoot(bullet_group,pocisk_img,(-1, 0)) 
    if keys[pygame.K_RIGHT]:
        player.shoot(bullet_group,pocisk_img,(1, 0))
    
    # Зберігаємо попередню позицію гравця
    old_x, old_y = player.rect.x, player.rect.y
    player.update(keys)

    # Перевіряємо колізії з перешкодами
    for obstacle_rect in current_room.obstacle_rects:
        if player.rect.colliderect(obstacle_rect):
            # Повертаємо гравця на попередню позицію
            player.rect.x, player.rect.y = old_x, old_y
            break

    door_width, door_thickness = 60, 10
    mid_x, mid_y = WIDTH // 2, HEIGHT // 2

    if player.rect.top <= 150 and not (current_room.doors["N"] and mid_x - door_width//2 <= player.rect.centerx <= mid_x + door_width//2):
        player.rect.top = 150

    if player.rect.bottom >= HEIGHT - 150 and not (current_room.doors["S"] and mid_x - door_width//2 <= player.rect.centerx <= mid_x + door_width//2):
        player.rect.bottom = HEIGHT - 150

    if player.rect.left <= 150 and not (current_room.doors["W"] and mid_y - door_width//2 <= player.rect.centery <= mid_y + door_width//2):
        player.rect.left = 150

    if player.rect.right >= WIDTH - 150 and not (current_room.doors["E"] and mid_y - door_width//2 <= player.rect.centery <= mid_y + door_width//2):
        player.rect.right = WIDTH - 150

    for enemy in enemy_group:
        if isinstance(enemy, EnemyStandShoot):
            enemy.update(player, enemy_bullets, enemy_bullet_img)# po to zeby dac przeciwnikowi dane o graczu gdzie jest i dodawalo jego pociski do grupy enemy bullet
        elif isinstance(enemy, EnemyCharge):
            enemy.update(player) # info o graczu
        clash.update(enemy, player) #zderzanie i odpych
    enemy_bullets.update()

    # Перевіряємо чи кімната очищена (немає живих ворогів)
    if current_coords == (4, 2):  # Кімната з босом
        current_room.cleared = boss is None
    else:
        current_room.cleared = len(enemy_group) == 0

    if transition_cooldown > 0:
        transition_cooldown -= 1

    if transition_cooldown == 0:
        if player.rect.left <= 150:
            if current_room.doors["W"] and mid_y - door_width//2 <= player.rect.centery <= mid_y + door_width//2:
                next_coords = (current_coords[0] - 1, current_coords[1])
                if next_coords in current_room.neighbors and current_room.cleared:
                    current_coords = next_coords
                    current_room = game_map.rooms[current_coords]
                    player.rect.right = WIDTH - 6 - door_thickness - 150
                    transition_cooldown = 10
                    # Створюємо ворогів для нової кімнати
                    spawn_enemies_for_room(current_coords)
                else:
                    player.rect.left = 150
            else:
                player.rect.left = 150

        elif player.rect.right >= WIDTH - 150:
            if current_room.doors["E"] and mid_y - door_width//2 <= player.rect.centery <= mid_y + door_width//2:
                next_coords = (current_coords[0] + 1, current_coords[1])
                if next_coords in current_room.neighbors and current_room.cleared:
                    current_coords = next_coords
                    current_room = game_map.rooms[current_coords]
                    player.rect.left = 6 + door_thickness + 150
                    transition_cooldown = 10
                    # Створюємо ворогів для нової кімнати
                    spawn_enemies_for_room(current_coords)
                else:
                    player.rect.right = WIDTH - 150
            else:
                player.rect.right = WIDTH - 150

        elif player.rect.top <= 150:
            if current_room.doors["N"] and mid_x - door_width//2 <= player.rect.centerx <= mid_x + door_width//2:
                next_coords = (current_coords[0], current_coords[1] - 1)
                if next_coords in current_room.neighbors and current_room.cleared:
                    current_coords = next_coords
                    current_room = game_map.rooms[current_coords]
                    player.rect.bottom = HEIGHT - 6 - door_thickness - 150
                    transition_cooldown = 10
                    # Створюємо ворогів для нової кімнати
                    spawn_enemies_for_room(current_coords)
                else:
                    player.rect.top = 150
            else:
                player.rect.top = 150

        elif player.rect.bottom >= HEIGHT - 150:
            if current_room.doors["S"] and mid_x - door_width//2 <= player.rect.centerx <= mid_x + door_width//2:
                next_coords = (current_coords[0], current_coords[1] + 1)
                if next_coords in current_room.neighbors and current_room.cleared:
                    current_coords = next_coords
                    current_room = game_map.rooms[current_coords]
                    player.rect.top = 6 + door_thickness + 150
                    transition_cooldown = 10
                    # Створюємо ворогів для нової кімнати
                    spawn_enemies_for_room(current_coords)
                else:
                    player.rect.bottom = HEIGHT - 150
            else:
                player.rect.bottom = HEIGHT - 150
    current_room.draw(screen)
    screen.blit(player.image, player.rect)
    #generowanie przeciwników
    enemy_group.draw(screen)#zebyrysowalo innych wrogow
    enemy_bullets.draw(screen)
    bullet_group.update()
    bullet_group.draw(screen)
    #generowanie bossa
    if boss is not None:
        for bullet in bullet_group:
            if boss.rect.inflate(-boss.rect.width * 0.6, -boss.rect.height * 0.6).colliderect(bullet.rect.inflate(-bullet.rect.width * 0.6, -bullet.rect.height * 0.6)):
                boss.hp -= player.stats["damage"]
                bullet.kill()

        if boss.hp > 0:
            boss.update(enemy_bullets)
            screen.blit(boss.image, boss.rect)
            boss.draw_health_bar(screen)
        else:
            boss_death_pos = boss.rect.center
            death_anim = Boss_defeated(boss_death_pos)
            boss_death_group.add(death_anim)
            boss = None
    for enemy in enemy_group:
        for bullet in bullet_group:
            if enemy.rect.inflate(-enemy.rect.width * 0.7, -enemy.rect.height * 0.6).colliderect(bullet.rect.inflate(-bullet.rect.width * 0.7, -bullet.rect.height * 0.6)):
                enemy.health -= player.stats["damage"]# po trafieniu zadaje damage z statystyk potem pocisk znika
                bullet.kill()
                if enemy.health <= 0:# ze jesli przecwnik nie ma zycia to zeby zniknol 
                    enemy.kill()
                    
    boss_death_group.update()
    boss_death_group.draw(screen)

    if player.health > 0 and invicible==0:
        for bullet in enemy_bullets:
            #zmniejszanie hit boxow gracza o ile % mniejsze oraz pocisku (potrzebne z powodu tego ze wokol obrazka jest kwadrat go otaczajacy )
            if player.rect.inflate(-player.rect.width * 0.6, -player.rect.height * 0.6).colliderect(bullet.rect.inflate(-bullet.rect.width * 0.6, -bullet.rect.height * 0.6)):
                player.health -= 0.5
                bullet.kill()
                pain = 30 #przez ile czuje bol
                invicible =60 # czas nietykalnosci gracza
                break # zeby nie dostal wielu dmg w jednej klatce
    
    elif player.health <= 0:
        if death_couldown <=90: # planowana animacja
           player.alive = False # mozliwosc ruszania 
           image = pygame.image.load("assets/smierc.png").convert_alpha()#obrazek smierci gracza
           player.image = pygame.transform.scale(image, (150, 150))
        if death_couldown == 0:
            running = False
        else:
            death_couldown -= 1
    if player.alive :
        if pain == 30:# obrazek bolu gracza 
            image = pygame.image.load("assets/hit_taken.png").convert_alpha()#na jaki obrazek
            player.image = pygame.transform.scale(image, (150, 150))  #wielkosc (potrzebna przez zmiane hitboxow)
        elif pain ==0:
            image = pygame.image.load("assets/character.png").convert_alpha()# przywrocenie obrazka
            player.image = pygame.transform.scale(image, (150, 150))
    if pain !=0:
        pain -=1
    if invicible > 0 :# zeby nam nie wchodzilo na minus
        invicible -=1
    if boss is None and not boss_defeated_animation_done and len(boss_death_group) == 0 and boss_death_pos is not None: #sprawdzamy czy animacja sie zakonczyla i dodajemy trofeum
        trophy = Boss_trophy(boss_death_pos)
        trophy_group.add(trophy)
        boss_defeated_animation_done = True

    trophy_group.draw(screen)
    ui.draw(screen) #ostatnie, by nic nie zasloniło

    pygame.display.flip()

pygame.quit()